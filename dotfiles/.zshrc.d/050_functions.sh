# ata aliases
export TWS_ACCOUNT="DU187075"
export DATABENTO_API_KEY="db-u3KjeQxvdc9XStbGmUsUfGFwkBaTM"
export GEMINI_API_KEY="AIzaSyA6bbw7m1yCzQQSG9b5eDG8SRbMkIaM-_s"
export JUPYTER_DEFAULT_KERNEL="Python 3 (ipykernel)"

alias edit_bash='zed ~/.zshrc.d/050_functions.sh'
alias source_bash='source ~/.zshrc'

alias jup='jupyter notebook . --port=8888'
alias lab='jupyter lab . --port=8888'

alias tws='sh /opt/ibc/twsstartmacos.sh'

alias ls='eza'
alias ll='eza -la'
alias cat='bat'

alias git_push='git push --force'
alias git_ref='git reflog --date=iso'

custom_rebase() {
    local current_branch=$(git rev-parse --abbrev-ref HEAD)
    local commits=${1:-0} # Note: This still only captures the very first argument
    local only_reset=false
    local interactive_mode=false

    # Function to parse options
    while [[ $# -gt 0 ]]; do
        case "$1" in
            --reset)
                only_reset=true
                shift
                ;;
            --interactive) # Corrected position
                interactive_mode=true
                shift # Consume --interactive
                ;;
            *) # Catch-all for non-flag arguments
                # Assume it's the number of commits if not an option
                # This logic might overwrite commits if flags appear after a number
                if [[ "$1" -gt 0 ]]; then
                    commits="$1"
                fi
                shift # Consume the argument
                ;;
        esac
    done

    if ! "$only_reset"; then
        # use git stash clear sometimes if a previous stash is still there
        git stash push -u

        if [ "$current_branch" = "develop" ]; then
            git pull origin develop
        else
            git fetch origin develop:develop

            if "$interactive_mode"; then
                git rebase develop -i
            else
                git rebase develop
            fi
        fi

        if [ -n "$(git stash list)" ]; then
            git stash pop
        fi
    fi

    # Uses the original reset logic from the user's code
    if [ "$commits" -gt 0 ]; then
        git reset HEAD~"$commits" --soft
    fi
}

git_reset() {
  git reset --hard
  git pull
}

# Function to checkout a branch from a GitHub URL
# Usage: checkout_branch_from_url <github-url>
# Example: checkout_branch_from_url https://github.com/stastnypremysl/nautilus_trader/tree/propagate-start-stop-in-request-data
checkout_branch_from_url() {
    # Check if URL is provided
    if [ $# -eq 0 ]; then
        echo "Error: No GitHub URL provided"
        echo "Usage: checkout_branch_from_url <github-url>"
        echo "Example: checkout_branch_from_url https://github.com/stastnypremysl/nautilus_trader/tree/propagate-start-stop-in-request-data"
        return 1
    fi

    local URL="$1"

    # Validate URL format
    if [[ ! "$URL" =~ ^https://github\.com/.+/.+/tree/.+ ]]; then
        echo "Error: Invalid GitHub URL format"
        echo "Expected format: https://github.com/owner/repo/tree/branch-name"
        return 1
    fi

    # Parse the URL to extract components
    local URL_PATH="${URL#https://github.com/}"
    local IFS='/'
    local PARTS=(${=URL_PATH})  # Use ${=} for proper word splitting in zsh

    if [ ${#PARTS[@]} -lt 4 ]; then
        echo "Error: Invalid URL structure"
        return 1
    fi

    local OWNER="${PARTS[1]}"
    local REPO="${PARTS[2]}"
    # Skip "tree" part (PARTS[3]) and get branch name (join remaining parts with '/')
    local BRANCH_NAME=""
    for ((i=4; i<=${#PARTS[@]}; i++)); do
        if [ -z "$BRANCH_NAME" ]; then
            BRANCH_NAME="${PARTS[i]}"
        else
            BRANCH_NAME="$BRANCH_NAME/${PARTS[i]}"
        fi
    done

    echo "Parsed URL:"
    echo "  Owner: $OWNER"
    echo "  Repository: $REPO"
    echo "  Branch: $BRANCH_NAME"
    echo ""

    # Generate remote name (use owner name, replace special chars with underscores)
    local REMOTE_NAME=$(echo "$OWNER" | sed 's/[^a-zA-Z0-9]/_/g')

    # Check if we're in a git repository
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        echo "Error: Not in a git repository"
        return 1
    fi

    # Check if remote already exists
    if git remote get-url "$REMOTE_NAME" > /dev/null 2>&1; then
        echo "Remote '$REMOTE_NAME' already exists"
        local EXISTING_URL=$(git remote get-url "$REMOTE_NAME")
        local EXPECTED_URL="https://github.com/$OWNER/$REPO.git"

        if [ "$EXISTING_URL" != "$EXPECTED_URL" ]; then
            echo "Warning: Existing remote URL ($EXISTING_URL) doesn't match expected URL ($EXPECTED_URL)"
            echo "Updating remote URL..."
            git remote set-url "$REMOTE_NAME" "$EXPECTED_URL"
        fi
    else
        echo "Adding remote '$REMOTE_NAME'..."
        git remote add "$REMOTE_NAME" "https://github.com/$OWNER/$REPO.git"
    fi

    # Fetch from the remote
    echo "Fetching from remote '$REMOTE_NAME'..."
    git fetch "$REMOTE_NAME"

    # Check if the branch exists on the remote
    local REMOTE_BRANCH="$REMOTE_NAME/$BRANCH_NAME"
    if ! git show-ref --verify --quiet "refs/remotes/$REMOTE_BRANCH"; then
        echo "Error: Branch '$BRANCH_NAME' not found on remote '$REMOTE_NAME'"
        echo "Available branches on $REMOTE_NAME:"
        git branch -r | grep "$REMOTE_NAME/" | sed "s/.*$REMOTE_NAME\//  /"
        return 1
    fi

    # Check if local branch already exists
    if git show-ref --verify --quiet "refs/heads/$BRANCH_NAME"; then
        echo "Local branch '$BRANCH_NAME' already exists"
        echo "Switching to existing branch and updating..."
        git switch "$BRANCH_NAME"
        git reset --hard "$REMOTE_BRANCH"
    else
        echo "Creating and switching to new branch '$BRANCH_NAME'..."
        git switch -c "$BRANCH_NAME" "$REMOTE_BRANCH"
    fi

    echo ""
    echo "✅ Successfully checked out branch '$BRANCH_NAME'"
    echo "Current branch: $(git branch --show-current)"
    echo "Latest commit: $(git log -1 --oneline)"
}

# Reversed tailf
#
# Usage: "flait logfile"
railf() {
    while true; do
        clear
        tail -n $(($LINES - 1)) $1 |
            tac |
            cut -c 1-$COLUMNS
        sleep 1
    done
}

# CD to a directory, but open it in vim, if it is a file
cd() {
    if [ -f "${1}" ]; then
        vim "${@}"
    else
        builtin cd "${@}"
    fi
}

# make a directory and cd to it
mkcd() {
    if [ ! -d "${1}" ]; then
        mkdir -p "${1}"
    fi
    cd "${1}"
}
#
# make a directory with prefixed date and cd to it
mkcdd() {
    local name
    name="$(date "+%Y_%m_%d")_${1}"
    if [ ! -d "${name}" ]; then
        mkdir -p "${name}"
    fi
    cd "${name}"
}

# Define a more convinient way of extracting colums using awk
# Usage column <colnumber> [<optional_column seperator>] < infile
col() {
    eval awk ${2:+"-F $2"} "'{print \$$1}'"
}

# In honor to Star-Trek
makeitso() {
    sudo $(history 2 | head -n 1 | sed -e 's@^[0-9]\+\s\+@@')
}

# Copy current working directory to clipboard AND output it
cwd() {
    pwd | tr -d '\n' | pbcopy
    pwd
}

# Rename using a temporary step to overcome hfs case-insensitivity
mvcase() {
    local source="${1}"
    local destination="${2}"

    local uuid="$(uuidgen)"

    while [ -e "${source}.${uuid}" ]; do
        uuid="$(uuidgen)"
    done

    mv "${source}" "${source}.${uuid}"
    mv "${source}.${uuid}" "${destination}"
}

# Rename using a temporary step to overcome hfs case-insensitivity, but for git
gitmvcase() {
    local source="${1}"
    local destination="${2}"

    local uuid="$(uuidgen)"

    while [ -e "${source}.${uuid}" ]; do
        uuid="$(uuidgen)"
    done

    git mv "${source}" "${source}.${uuid}"
    git mv "${source}.${uuid}" "${destination}"
}

wait_for() {
    if [ ${#} -ne 1 ]; then
        echo "usage: ${FUNCNAME[0]} <hostname>"
        return 1
    fi

    local host="${1}"

    local wait_option='-w'
    if [ "$(uname)" = "Darwin" ]; then
        wait_option='-t'
    fi

    local nl=''
    while ! ping -qc1 "${wait_option}2" "${host}" >/dev/zero 2>&1; do
        # make sure we have a chance to cancel the loop:
        sleep 0.5

        echo -n '.'
        nl='\n'
    done

    printf "${nl}Host %s is now available\n" "${host}"
}
compdef _hosts wait_for
compdef _known_hosts wait_for

wait_for_port() {
    if [ ${#} -ne 2 ]; then
        echo "usage: ${FUNCNAME[0]} <hostname> <port>"
        return 1
    fi

    local host="${1}"
    local port="${2}"

    local nl=''
    while ! nc -zG 2 "${host}" "${port}" >/dev/zero; do
        # make sure we have a chance to cancel the loop:
        sleep 0.5

        echo -n '.'
        nl='\n'
    done

    printf "${nl}port %s on %s is now available\n" "${port}" "${host}"
}
compdef _hosts wait_for_port
compdef _known_hosts wait_for_port

wait_for_ssh() {
    local host="$(ssh -G "${@}" | grep '^hostname ' | sed 's/^hostname //')"
    local port="$(ssh -G "${@}" | grep '^port ' | sed 's/^port //')"

    wait_for_port "${host}" "${port}"
    ssh "${@}"
}

_wait_for_ssh() {
    local service='ssh'
    _ssh
}

compdef _wait_for_ssh wait_for_ssh

retag() {
    if [ "$#" -ne "1" ]; then
        echo "Provide a tag name!"
        return 1
    fi
    local TAG="$1"
    git push --delete origin "$TAG"
    git tag -d "$TAG"
    git tag "$TAG"
    git push --tags
}

rmtag() {
    if [ "$#" -ne "1" ]; then
        echo "Provide a tag name!"
        return 1
    fi
    local TAG="$1"

    git tag -d "$TAG"
    git push --delete origin "$TAG"
}

push_nexus() {
    if [ "$#" -ne "2" ]; then
        echo "Provide a image name and tag name!"
        return 1
    fi
    local image="$1"
    local tag="$2"

    docker pull registry.gitlab.com/ekkogmbh/artifacts/${image}:${tag} &&
        docker image tag registry.gitlab.com/ekkogmbh/artifacts/${image}:${tag} lpitdnexus01.bmwgroup.net:16052/epaper/${image}:${tag} &&
        docker push lpitdnexus01.bmwgroup.net:16052/epaper/${image}:${tag}
}

# Allow to "Yoink" something directly from my commandline either by specifying it or using fzf in the current directory
yoink() {
    if [ "$#" -gt 0 ]; then
        open -a Yoink "$@"
    else
        local result
        result="$(fzf)"
        if [ -n "${result}" ]; then
            open -a Yoink "${result}"
        fi
    fi
}

## Generic function to clone github or gitlab repositories to the correct dev folder on *my* system
## which currently is ~/Development/<github|gitlab>/<org|author>/<reponame>
##
## The function is usually called using the aliases ghclone and glclone
gitclone() {
    if [ "$#" -lt 2 ]; then
        echo "gitclone <gitlab|github> [--ssh] <ssh-url|http(s)-url|org/name>"
        return 1
    fi

    local ssh=""
    local mode=""
    local repo=""

    while [ "$#" -gt 0 ]; do
        if [[ "$1" == "--ssh" ]]; then
            ssh="true"
            shift
            continue
        fi

        if [ -z "$mode" ]; then
            mode="$1"
            shift
            continue
        fi

        if [ -z "$repo" ]; then
            repo="$1"
            shift
            continue
        fi

        break
    done

    # Extract org/name
    local org=""
    local name=""
    if [[ "${repo}" =~ ^https?://.+/(.+)/(.+)\.git$ ]]; then
        org="${match[1]}"
        name="${match[2]}"
    elif [[ "${repo}" =~ ^git@.+:(.+)/(.+)\.git$ ]]; then
        org="${match[1]}"
        name="${match[2]}"
        ssh="true"
    elif [[ "${repo}" =~ ^([^/]+)/([^/]+)$ ]]; then
        org="${match[1]}"
        name="${match[2]}"
    else
        echo "The given repository does not seem to be an http(s), ssh or org/name identifier: $repo"
        return 1
    fi

    if [[ "$mode" != "github" ]] && [[ "$mode" != "gitlab" ]]; then
        echo "Unsupported mode: $mode"
        return 1
    fi

    local target_without_name="${HOME}/Development/${mode}/${org}"
    local target="${target_without_name}/${name}"

    if [ -d "$target" ]; then
        echo "Target directory already exists. Just changing into it: ${target}"
        cd "${target}" || return 1
        return 0
    fi

    local url=""
    if [ -n "${ssh}" ]; then
        url="git@${mode}.com:${org}/${name}.git"
    else
        url="https://${mode}.com/${org}/${name}.git"
    fi

    mkcd "${target_without_name}"
    git clone "${url}" "${name}"
    cd "${name}" || return 1
}

ghclone() {
    gitclone github "$@"
}

glclone() {
    gitclone gitlab "$@"
}

# Recompress any archive using xz -9
rexz() {
    if [ $# -ne 1 ]; then
        echo "Usage: rexz <compressed-file>"
        return 1
    fi

    local input="$1"
    local output="${input%.*}.xz"

    if [ ! -f "$input" ]; then
        echo "Error: File '$input' does not exist"
        return 1
    fi

    local initial_size
    initial_size=$(stat -c %s "$input")

    declare -a decompress_cmd
    case "$input" in
    *.gz)
        decompress_cmd=("gunzip" "-c")
        ;;
    *.bz2)
        decompress_cmd=("bunzip2" "-c")
        ;;
    *.xz)
        decompress_cmd=("unxz" "-c")
        ;;
    *.zst)
        decompress_cmd=("zstd" "-d" "-c")
        ;;
    *.lz4)
        decompress_cmd=("lz4" "-d" "-c")
        ;;
    *.lzo)
        decompress_cmd=("lzop" "-d" "-c")
        ;;
    *.Z)
        decompress_cmd=("uncompress" "-c")
        ;;
    *.7z)
        decompress_cmd=("7z" "e" "-so")
        ;;
    *)
        echo "Error: Unsupported compression format"
        return 1
        ;;
    esac

    echo "Recompressing $input ($initial_size bytes) to $output..."

    pv -c -N "Compressed" "$input" | "${decompress_cmd[@]}" | pv -c -N "Decompressed" | xz -T0 -9 | pv -c -N "Recompressed" >"$output"

    local final_size
    final_size=$(stat -c %s "$output")
    local diff=$((initial_size - final_size))
    local percent=$(((diff * 100) / initial_size))

    echo "Complete: $input ($initial_size bytes) -> $output ($final_size bytes)"
    echo "Difference: $diff bytes ($percent% $([ $diff -gt 0 ] && echo "smaller" || echo "larger"))"
}

# Decode a JWT token into header and body struct
function jwt-decode() {
    jq -R 'split(".") | .[0],.[1] | @base64d | fromjson' <<<"$1"
}
